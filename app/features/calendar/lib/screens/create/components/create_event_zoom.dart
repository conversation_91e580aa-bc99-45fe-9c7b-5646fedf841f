import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/widgets/gradient_box_border.dart';
import 'package:gp_feat_calendar/screens/create/create_calendar_event_controller.dart';

import 'create_event_section.dart';

class CreateEventGGMeet extends GetView<CreateCalendarEventController> {
  const CreateEventGGMeet({
    super.key,
  });

  @override
  CreateCalendarEventController get controller =>
      Get.put(CreateCalendarEventController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ExpandableNotifier(
        controller: controller.meetExpanableController,
        child: Expandable(
          collapsed: const Row(),
          expanded: CreateEventSection(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        LocaleKeys.calendar_create_label_meet.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.copyWith(height: 1),
                      ),
                    ),
                    SizedBox(
                      width: 40,
                      height: 26,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: CupertinoSwitch(
                          value: controller.hasMeet.value,
                          onChanged: controller.onMeetChanged,
                          activeTrackColor: GPColor.functionAccentWorkSecondary,
                        ),
                      ),
                    ),
                  ],
                ).paddingSymmetric(horizontal: 16, vertical: 12),
                Obx(() => controller.hasMeet.value
                    ? Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 16, right: 16, bottom: 12),
                            child: Row(
                              children: [
                                if (controller.googleEmail.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: SvgWidget(
                                      'assets/images/svg/ic24-fill-person-checkmark.svg',
                                      width: 16,
                                      height: 16,
                                      color:
                                          GPColor.functionAccentWorkSecondary,
                                    ),
                                  ),
                                Expanded(
                                  child: Text(
                                    controller.googleEmail.isEmpty
                                        ? LocaleKeys
                                            .calendar_create_not_google_signed_in
                                            .tr
                                        : controller.googleEmail,
                                    maxLines: controller.googleEmail.isEmpty
                                        ? null
                                        : 1,
                                    overflow: controller.googleEmail.isEmpty
                                        ? null
                                        : TextOverflow.ellipsis,
                                    style: textStyle(GPTypography.bodyMedium)
                                        ?.copyWith(
                                            color: GPColor.contentSecondary),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // AI Assistant Section
                          // _buildAIAssistantSection(),
                          AIAssistantBox(
                            controller: controller,
                          )
                        ],
                      )
                    : const SizedBox())
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAIAssistantSection() {
    return Obx(() => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              // Divider
              Container(
                height: 1,
                color: GPColor.linePrimary,
              ),
              const SizedBox(height: 12),

              // AI Assistant Header with Switch
              Row(
                children: [
                  // Avatar
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: GPColor.contentQuaternary,
                        width: 1,
                      ),
                      gradient: const LinearGradient(
                        colors: [Color(0xFF4BAFF6), Color(0xFFFBA446)],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: const Center(
                      child: Text(
                        'AI',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Title and Bot Tag
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          'AI GapoWork Trợ lý cuộc họp',
                          style: textStyle(GPTypography.headingMedium)
                              ?.copyWith(height: 1),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(width: 4),
                        // Bot Tag
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: GPColor.blue,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Bot',
                            style: textStyle(GPTypography.bodySmall)?.copyWith(
                              color: GPColor.blue,
                              height: 1.33,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Switch
                  SizedBox(
                    width: 40,
                    height: 26,
                    child: FittedBox(
                      fit: BoxFit.contain,
                      child: CupertinoSwitch(
                        value: controller.hasAIAssistant.value,
                        onChanged: controller.onAIAssistantChanged,
                        activeTrackColor: GPColor.functionAccentWorkSecondary,
                      ),
                    ),
                  ),
                ],
              ),
              // Description when AI Assistant is enabled
              const SizedBox(height: 8),
              Text(
                'Trợ lý cuộc họp sẽ tham gia Google meet và ghi chú lại nội dung quan trọng',
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
              const SizedBox(height: 12),
            ],
          ),
        ));
  }
}

class AIAssistantBox extends StatelessWidget {
  const AIAssistantBox({super.key, required this.controller});

  final CreateCalendarEventController controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: GradientBoxBorder(
              gradient: const LinearGradient(
            colors: [Color(0xFF4BAFF6), Color(0xFFFBA446)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          )),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Column(
            children: [
              Row(
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: Image.asset(Assets
                            .PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG),
                      ),
                      Positioned(
                        right: -4,
                        bottom: -4,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: GPColor.blue,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Bot',
                            style:
                                textStyle(GPTypography.headingSmall)?.copyWith(
                              color: GPColor.blue,
                              height: 1.33,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                      child: Text(
                    "Thêm Trợ lý AI vào cuộc họp",
                    style: textStyle(GPTypography.headingSmall),
                  )),
                  const SizedBox(width: 12),
                  Obx(() => SizedBox(
                        width: 40,
                        height: 26,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: CupertinoSwitch(
                            value: controller.hasAIAssistant.value,
                            onChanged: controller.onAIAssistantChanged,
                            activeTrackColor:
                                GPColor.functionAccentWorkSecondary,
                          ),
                        ),
                      )),
                ],
              ),
              const SizedBox(height: 18),
              Text(
                "🚀  Cho phép trợ lý cuộc họp tham gia và ghi chú nội dung quan trọng",
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    "⌛ Số lượt còn lại:",
                    style: textStyle(GPTypography.bodyMedium)
                        ?.copyWith(color: GPColor.contentSecondary),
                  ),
                  const SizedBox(width: 4),
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                          text: "3",
                          style: textStyle(GPTypography.headingSmall)?.copyWith(
                              color: GPColor.functionAccentWorkSecondary)),
                      TextSpan(
                          text: "/5",
                          style: textStyle(GPTypography.bodyMedium)
                              ?.copyWith(color: GPColor.contentSecondary)),
                    ]),
                  ),
                  const SizedBox(width: 4),
                  InkWell(
                    onTap: () => _showAIAssistantInfoBottomSheet(),
                    child: SvgWidget(
                      Assets
                          .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_INFORMATIONMARK_CIRCLE_SVG,
                      color: GPColor.contentSecondary,
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  void _showAIAssistantInfoBottomSheet() {
    Popup.instance.showBottomSheet(
      const AIAssistantInfoBottomSheet(),
      isScrollControlled: true,
      isDismissible: true,
    );
  }
}

class AIAssistantInfoBottomSheet extends StatelessWidget {
  const AIAssistantInfoBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main content
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Header with avatar and title
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Avatar
                    SizedBox(
                      width: 48,
                      height: 48,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: Image.asset(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Title
                    Text(
                      'Bạn đang sử dụng gói dùng thử dành cho Trợ lý cuộc họp',
                      style: textStyle(GPTypography.headingXLarge),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                // Information list
                Column(
                  children: [
                    _buildInfoItem(
                      '👉',
                      'Chatbot được sử dụng tối đa 10 cuộc họp/ tuần để tham gia cuộc họp trong tổ chức của bạn',
                    ),
                    const SizedBox(height: 16),
                    _buildInfoItem(
                      '👉',
                      'Tối đa 2 thành viên được quyền thêm chatbot hỗ trợ cuộc họp',
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Divider
          Divider(
            color: GPColor.lineTertiary,
            height: 1,
            thickness: 1,
            indent: 24,
            endIndent: 24,
          ),
          // Info section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: GPColor.blueLighter,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                SvgWidget(
                  Assets
                      .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_INFORMATIONMARK_CIRCLE_SVG,
                  width: 16,
                  height: 16,
                  color: GPColor.contentPrimary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Số cuộc họp sẽ được đặt lại về 10 vào 0h ngày 1/9/2025',
                    style: textStyle(GPTypography.bodyLarge),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            color: GPColor.lineTertiary,
            height: 1,
            thickness: 1,
          ),
          // Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SizedBox(
              width: double.infinity,
              child: GPWorkButton(
                title: 'Nâng cấp ngay',
                onTap: () {
                  Get.back();
                  // TODO: Handle upgrade action
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String emoji, String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          emoji,
          style: textStyle(GPTypography.bodyMedium),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: textStyle(GPTypography.bodyLarge),
          ),
        ),
      ],
    );
  }
}
